<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>RansomAware Security Dashboard</title>
    <!-- Bootstrap 5 for modern UI -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Chart.js for graphs -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- jsPDF for PDF generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-bg: #0f172a;
            --card-bg: #1e293b;
            --border-color: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        body {
            background: linear-gradient(135deg, var(--dark-bg) 0%, #1e293b 100%);
            color: var(--text-primary);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        .navbar {
            background: rgba(15, 23, 42, 0.95) !important;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-color);
            box-shadow: var(--shadow);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--text-primary) !important;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-online {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .status-indicator {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: currentColor;
        }

        .main-container {
            padding: 2rem 0;
        }

        .card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.25);
        }

        .card-header {
            background: rgba(37, 99, 235, 0.1);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 1.5rem;
            border-radius: 0;
        }

        .card-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-primary);
        }

        .card-body {
            padding: 1.5rem;
        }

        .table-dark {
            --bs-table-bg: transparent;
            --bs-table-border-color: var(--border-color);
            margin: 0;
        }

        .table-dark th {
            background: rgba(37, 99, 235, 0.1);
            border-color: var(--border-color);
            font-weight: 600;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 0.75rem;
            color: var(--text-secondary);
        }

        .table-dark td {
            border-color: var(--border-color);
            vertical-align: middle;
            padding: 0.75rem;
            font-size: 0.875rem;
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
            padding: 0.5rem 1rem;
            transition: all 0.2s ease;
            border: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
            color: white;
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: #000;
        }

        .btn-warning:hover {
            background: #d97706;
            color: #000;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-secondary {
            background: var(--secondary-color);
            color: white;
        }

        .btn-outline-primary {
            background: transparent;
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            color: white;
        }

        /* Alert animations */
        .blinking {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                background-color: rgba(239, 68, 68, 0.1);
                color: var(--danger-color);
            }
            50% {
                background-color: rgba(239, 68, 68, 0.3);
                color: #fff;
            }
            100% {
                background-color: rgba(239, 68, 68, 0.1);
                color: var(--danger-color);
            }
        }

        .scrollable-container {
            max-height: 400px;
            overflow-y: auto;
            border-radius: 8px;
        }

        .scrollable-container::-webkit-scrollbar {
            width: 6px;
        }

        .scrollable-container::-webkit-scrollbar-track {
            background: var(--card-bg);
        }

        .scrollable-container::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        .alert {
            border: none;
            border-radius: 8px;
            font-weight: 500;
            margin: 0;
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }

        .chart-container {
            position: relative;
            height: 300px;
            padding: 1rem 0;
        }

        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .metric-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
        }

        .metric-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .action-buttons {
            display: flex;
            gap: 0.75rem;
            flex-wrap: wrap;
            margin-top: 1rem;
        }

        .section-divider {
            margin: 2rem 0;
        }

        .badge {
            font-weight: 500;
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
        }

        .hidden {
            display: none !important;
        }

        .decoy-list-height {
            max-height: 200px;
        }

        /* Timeline Styles */
        .timeline {
            position: relative;
            padding-left: 2rem;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 0.75rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--border-color);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 1.5rem;
        }

        .timeline-marker {
            position: absolute;
            left: -2.25rem;
            top: 0.25rem;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid var(--card-bg);
        }

        .timeline-content {
            background: rgba(51, 65, 85, 0.3);
            padding: 1rem;
            border-radius: 8px;
            border-left: 3px solid var(--primary-color);
        }

        .communication-panel {
            background: rgba(51, 65, 85, 0.2);
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }

            .card-body {
                padding: 1rem;
            }

            .action-buttons {
                flex-direction: column;
            }

            .btn {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-shield-check"></i>
                RansomAware Security Dashboard
            </a>
            <div class="ms-auto">
                <span class="status-badge status-online">
                    <span class="status-indicator"></span>
                    System Online
                </span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container main-container">
        <!-- Metrics Overview -->
        <div class="metric-grid">
            <div class="metric-card">
                <div class="metric-value" id="totalEvents">0</div>
                <div class="metric-label">Total Events</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="activeAlerts">0</div>
                <div class="metric-label">Active Alerts</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="decoyFiles">0</div>
                <div class="metric-label">Decoy Files</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="systemUptime">99.9%</div>
                <div class="metric-label">System Uptime</div>
            </div>
        </div>

        <!-- Event Logs & Intrusion Alerts -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="bi bi-list-ul"></i>
                            Security Event Logs
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="scrollable-container">
                            <table class="table table-dark table-hover" id="logsTable">
                                <thead>
                                    <tr>
                                        <th>Index</th>
                                        <th>Timestamp</th>
                                        <th>Action</th>
                                        <th>File</th>
                                        <th>Local IP</th>
                                        <th>Public IP</th>
                                        <th>Location</th>
                                        <th>ISP</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Log entries will be injected here dynamically -->
                                </tbody>
                            </table>
                        </div>
                        <div class="action-buttons">
                            <button type="button" id="shareLogs" class="btn btn-outline-primary">
                                <i class="bi bi-share"></i>
                                Share Logs
                            </button>
                            <button type="button" id="downloadPDF" class="btn btn-secondary">
                                <i class="bi bi-download"></i>
                                Download PDF Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="bi bi-exclamation-triangle"></i>
                            Intrusion Alerts
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="scrollable-container" style="max-height: 300px;">
                            <div id="alerts" class="alert alert-danger hidden"></div>
                            <div id="noAlerts" class="text-center text-muted py-4">
                                <i class="bi bi-shield-check" style="font-size: 2rem;"></i>
                                <p class="mt-2 mb-0">No active alerts</p>
                            </div>
                        </div>
                        <div class="action-buttons">
                            <button type="button" id="lockdownButton" class="btn btn-outline-danger">
                                <i class="bi bi-lock"></i>
                                Emergency Lockdown
                            </button>
                            <button type="button" id="notifyButton" class="btn btn-warning">
                                <i class="bi bi-bell"></i>
                                Send Alert
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Digital Twin Integrity Status -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="bi bi-cpu"></i>
                            Digital Twin Integrity Status
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="twinStatus" class="alert alert-success">
                            <i class="bi bi-check-circle"></i>
                            All decoy files are consistent with the baseline.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Event Frequency Chart -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="bi bi-graph-up"></i>
                            Event Frequency Analysis
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="eventsChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- SOC Integration & Threat Intelligence -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="bi bi-shield-check"></i>
                            SOC Integration & Incident Management
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="d-grid gap-2">
                                    <button type="button" id="escalateToSOC" class="btn btn-danger">
                                        <i class="bi bi-shield-exclamation"></i>
                                        Escalate to SOC Team
                                    </button>
                                    <button type="button" id="createTicket" class="btn btn-info">
                                        <i class="bi bi-ticket-detailed"></i>
                                        Create SIEM Incident
                                    </button>
                                    <button type="button" id="notifyCSIRT" class="btn btn-warning">
                                        <i class="bi bi-people"></i>
                                        Alert CSIRT Team
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-grid gap-2">
                                    <button type="button" id="blockIOCs" class="btn btn-dark">
                                        <i class="bi bi-ban"></i>
                                        Block IOCs
                                    </button>
                                    <button type="button" id="quarantineAssets" class="btn btn-secondary">
                                        <i class="bi bi-shield-slash"></i>
                                        Quarantine Assets
                                    </button>
                                    <button type="button" id="generateIOCs" class="btn btn-success">
                                        <i class="bi bi-fingerprint"></i>
                                        Extract IOCs
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Threat Intelligence Panel -->
                        <div class="mt-4">
                            <h6 class="text-muted mb-3">
                                <i class="bi bi-globe"></i>
                                Threat Intelligence Integration
                            </h6>
                            <div class="row g-2">
                                <div class="col-md-3">
                                    <button type="button" id="checkVirusTotal" class="btn btn-outline-primary btn-sm w-100">
                                        <i class="bi bi-search"></i>
                                        VirusTotal Lookup
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button type="button" id="checkMISP" class="btn btn-outline-primary btn-sm w-100">
                                        <i class="bi bi-share"></i>
                                        MISP Query
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button type="button" id="checkOTX" class="btn btn-outline-primary btn-sm w-100">
                                        <i class="bi bi-eye"></i>
                                        AlienVault OTX
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button type="button" id="checkThreatFeed" class="btn btn-outline-primary btn-sm w-100">
                                        <i class="bi bi-rss"></i>
                                        Threat Feeds
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="bi bi-activity"></i>
                            Response Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="button" id="isolateEndpoint" class="btn btn-outline-danger">
                                <i class="bi bi-pc-display-horizontal"></i>
                                Isolate Endpoint
                            </button>
                            <button type="button" id="killProcesses" class="btn btn-outline-warning">
                                <i class="bi bi-stop-circle"></i>
                                Kill Suspicious Processes
                            </button>
                            <button type="button" id="collectForensics" class="btn btn-outline-info">
                                <i class="bi bi-hdd"></i>
                                Collect Forensics
                            </button>
                            <button type="button" id="backupCritical" class="btn btn-outline-success">
                                <i class="bi bi-cloud-upload"></i>
                                Backup Critical Data
                            </button>
                            <button type="button" id="activatePlaybook" class="btn btn-primary">
                                <i class="bi bi-book"></i>
                                Activate IR Playbook
                            </button>
                        </div>

                        <!-- Severity Level -->
                        <div class="mt-4">
                            <label for="severityLevel" class="form-label">Incident Severity</label>
                            <select id="severityLevel" class="form-select form-select-sm" title="Select incident severity level">
                                <option value="low">Low - Monitoring</option>
                                <option value="medium">Medium - Investigation</option>
                                <option value="high" selected>High - Active Threat</option>
                                <option value="critical">Critical - Breach</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- File Interaction Simulator & Decoy Control Panel -->
        <div class="row">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="bi bi-play-circle"></i>
                            Security Testing
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-3">Simulate ransomware attacks to test system response and detection capabilities.</p>
                        <button type="button" id="simulateAttack" class="btn btn-warning">
                            <i class="bi bi-bug"></i>
                            Simulate Ransomware Attack
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="bi bi-files"></i>
                            Decoy File Management
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="scrollable-container decoy-list-height">
                            <table class="table table-dark table-sm" id="decoysTable">
                                <thead>
                                    <tr>
                                        <th>Decoy File Name</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Decoy file list will be injected here -->
                                </tbody>
                            </table>
                        </div>
                        <div class="action-buttons">
                            <button type="button" id="refreshDecoys" class="btn btn-primary">
                                <i class="bi bi-arrow-clockwise"></i>
                                Refresh Files
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Incident Timeline & Communication -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="bi bi-clock-history"></i>
                            Incident Timeline & Communication Hub
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-8">
                                <div class="timeline-container">
                                    <h6 class="text-muted mb-3">Recent Activity Timeline</h6>
                                    <div id="incidentTimeline" class="timeline">
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-danger"></div>
                                            <div class="timeline-content">
                                                <small class="text-muted">Just now</small>
                                                <p class="mb-1"><strong>Ransomware Activity Detected</strong></p>
                                                <p class="mb-0 text-muted">Multiple decoy files accessed rapidly from IP *************</p>
                                            </div>
                                        </div>
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-warning"></div>
                                            <div class="timeline-content">
                                                <small class="text-muted">2 minutes ago</small>
                                                <p class="mb-1"><strong>Digital Twin Alert</strong></p>
                                                <p class="mb-0 text-muted">File integrity violation detected on financial_report.txt</p>
                                            </div>
                                        </div>
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-info"></div>
                                            <div class="timeline-content">
                                                <small class="text-muted">5 minutes ago</small>
                                                <p class="mb-1"><strong>System Monitoring Active</strong></p>
                                                <p class="mb-0 text-muted">RansomAware monitoring system initialized successfully</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="communication-panel">
                                    <h6 class="text-muted mb-3">Quick Communication</h6>
                                    <div class="d-grid gap-2">
                                        <button type="button" id="notifyManagement" class="btn btn-outline-primary btn-sm">
                                            <i class="bi bi-person-badge"></i>
                                            Notify Management
                                        </button>
                                        <button type="button" id="alertLegal" class="btn btn-outline-secondary btn-sm">
                                            <i class="bi bi-briefcase"></i>
                                            Alert Legal Team
                                        </button>
                                        <button type="button" id="contactVendors" class="btn btn-outline-info btn-sm">
                                            <i class="bi bi-telephone"></i>
                                            Contact Security Vendors
                                        </button>
                                        <button type="button" id="updateStatus" class="btn btn-outline-success btn-sm">
                                            <i class="bi bi-megaphone"></i>
                                            Update Status Page
                                        </button>
                                    </div>

                                    <div class="mt-4">
                                        <h6 class="text-muted mb-3">Compliance & Reporting</h6>
                                        <div class="d-grid gap-2">
                                            <button type="button" id="generateCompliance" class="btn btn-outline-warning btn-sm">
                                                <i class="bi bi-file-earmark-text"></i>
                                                Generate Compliance Report
                                            </button>
                                            <button type="button" id="notifyRegulators" class="btn btn-outline-danger btn-sm">
                                                <i class="bi bi-building"></i>
                                                Notify Regulators
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery (required for Bootstrap's JavaScript plugins) -->
    <script src="https://code.jquery.com/jquery-3.2.1.slim.min.js"></script>
    
    <script>
        let chart;  // Global variable for Chart.js instance

        // Update metrics in the dashboard
        function updateMetrics(data, alerts, decoyCount) {
            document.getElementById('totalEvents').textContent = data.length;
            document.getElementById('activeAlerts').textContent = alerts.length;
            document.getElementById('decoyFiles').textContent = decoyCount;
        }

        // Fetch logs from the backend API and update Digital Twin status
        function fetchLogs() {
            fetch('/api/logs')
                .then(response => response.json())
                .then(data => {
                    const tableBody = document.querySelector('#logsTable tbody');
                    if (!tableBody) return; // Prevent errors if tbody is missing
                    tableBody.innerHTML = '';
                    let eventCounts = {};
                    let alerts = [];
                    let twinFlag = false; // Flag for digital twin deviations
                    let twinAlertFiles = [];  // To store details of files causing TWIN_ALERT

                    data.forEach((block) => {
                        const row = document.createElement('tr');
                        const date = new Date(block.timestamp * 1000);
                        const timeStr = date.toLocaleString();
                        const action = block.event_data ? block.event_data.action : 'N/A';
                        const file = block.event_data ? block.event_data.file : 'N/A';

                        const ipInfo = (block.event_data && block.event_data.ip_info) ? block.event_data.ip_info : {};
                        const { local_ip = "N/A", public_ip = "N/A", location = "N/A", isp = "N/A" } = ipInfo;

                        // Add action badge styling
                        let actionBadge = '';
                        if (action === 'ANOMALOUS_ACTIVITY') {
                            actionBadge = `<span class="badge bg-danger">${action}</span>`;
                        } else if (action === 'TWIN_ALERT') {
                            actionBadge = `<span class="badge bg-warning">${action}</span>`;
                        } else if (action === 'SIMULATED_ATTACK') {
                            actionBadge = `<span class="badge bg-info">${action}</span>`;
                        } else {
                            actionBadge = `<span class="badge bg-secondary">${action}</span>`;
                        }

                        row.innerHTML = `
                            <td><span class="badge bg-primary">${block.index}</span></td>
                            <td><small>${timeStr}</small></td>
                            <td>${actionBadge}</td>
                            <td><small>${file}</small></td>
                            <td><small>${local_ip}</small></td>
                            <td><small>${public_ip}</small></td>
                            <td><small>${location}</small></td>
                            <td><small>${isp}</small></td>
                        `;

                        // If it's a digital twin alert, record its file details
                        if (action === "TWIN_ALERT") {
                            twinFlag = true;
                            if (file && file.trim() !== "" && file !== "N/A") {
                                twinAlertFiles.push(file);
                            }
                        }

                        // Highlight rows with any alert and add to alerts array
                        if (action === "ANOMALOUS_ACTIVITY" || action === "SIMULATED_ATTACK" || action === "TWIN_ALERT") {
                            row.classList.add('blinking');
                            alerts.push(`<i class="bi bi-exclamation-triangle"></i> ${action} detected on ${file}`);
                        }

                        tableBody.appendChild(row);

                        // Group events for chart by hour:minute
                        if (block.event_data) {
                            const timeKey = date.getHours() + ':' + String(date.getMinutes()).padStart(2, '0');
                            eventCounts[timeKey] = (eventCounts[timeKey] || 0) + 1;
                        }
                    });

                    updateChart(eventCounts);
                    updateAlerts(alerts);

                    // Update Digital Twin Integrity Status
                    const twinDiv = document.getElementById('twinStatus');
                    if (twinFlag && twinAlertFiles.length > 0) {
                        let listHtml = "<i class='bi bi-exclamation-triangle'></i> <strong>Alert: Deviations detected in digital twin for file(s):</strong><br>";
                        listHtml += "<div style='max-height:150px; overflow-y:auto; margin-top: 0.5rem;'><ul class='mb-0'>";
                        twinAlertFiles.forEach(item => {
                            listHtml += "<li>" + item + "</li>";
                        });
                        listHtml += "</ul></div>";
                        twinDiv.innerHTML = listHtml;
                        twinDiv.className = "alert alert-danger";
                    } else if (twinFlag) {
                        twinDiv.innerHTML = "<i class='bi bi-exclamation-triangle'></i> Alert: Deviations detected in digital twin!";
                        twinDiv.className = "alert alert-danger";
                    } else {
                        twinDiv.innerHTML = "<i class='bi bi-check-circle'></i> All decoy files are consistent with the baseline.";
                        twinDiv.className = "alert alert-success";
                    }

                    // Update metrics
                    fetch('/api/decoys')
                        .then(response => response.json())
                        .then(decoys => {
                            updateMetrics(data, alerts, decoys.length);
                        })
                        .catch(() => updateMetrics(data, alerts, 0));
                })
                .catch(error => console.error('Error fetching logs:', error));
        }
        
        // Update or create the Chart.js bar chart for event frequency
        function updateChart(data) {
            const labels = Object.keys(data);
            const counts = Object.values(data);
            const ctx = document.getElementById('eventsChart').getContext('2d');

            if (chart) {
                chart.data.labels = labels;
                chart.data.datasets[0].data = counts;
                chart.update();
            } else {
                chart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Events per Minute',
                            data: counts,
                            backgroundColor: 'rgba(37, 99, 235, 0.1)',
                            borderColor: 'rgba(37, 99, 235, 1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4,
                            pointBackgroundColor: 'rgba(37, 99, 235, 1)',
                            pointBorderColor: '#fff',
                            pointBorderWidth: 2,
                            pointRadius: 4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                labels: {
                                    color: '#cbd5e1'
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    precision: 0,
                                    color: '#cbd5e1'
                                },
                                grid: {
                                    color: 'rgba(51, 65, 85, 0.5)'
                                }
                            },
                            x: {
                                ticks: {
                                    color: '#cbd5e1'
                                },
                                grid: {
                                    color: 'rgba(51, 65, 85, 0.5)'
                                }
                            }
                        }
                    }
                });
            }
        }

        // Update the Intrusion Alerts panel
        function updateAlerts(alerts) {
            const alertsDiv = document.getElementById('alerts');
            const noAlertsDiv = document.getElementById('noAlerts');

            if (alerts.length > 0) {
                alertsDiv.style.display = 'block';
                noAlertsDiv.style.display = 'none';
                alertsDiv.innerHTML = alerts.join('<hr class="my-2">');
            } else {
                alertsDiv.style.display = 'none';
                noAlertsDiv.style.display = 'block';
            }
        }
        
        // Fetch decoy file names from the backend
        function fetchDecoys() {
            fetch('/api/decoys')
                .then(response => response.json())
                .then(files => {
                    const decoysTable = document.querySelector('#decoysTable tbody');
                    decoysTable.innerHTML = '';
                    files.forEach(file => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td><i class="bi bi-file-text me-2"></i>${file}</td>
                            <td><span class="badge bg-success">Active</span></td>
                        `;
                        decoysTable.appendChild(row);
                    });
                })
                .catch(error => console.error('Error fetching decoy files:', error));
        }
        
        // Show toast notification
        function showToast(message, type = 'info') {
            const toastContainer = document.getElementById('toastContainer') || createToastContainer();
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type} border-0`;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            toastContainer.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();

            // Remove toast after it's hidden
            toast.addEventListener('hidden.bs.toast', () => {
                toast.remove();
            });
        }

        function createToastContainer() {
            const container = document.createElement('div');
            container.id = 'toastContainer';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '1055';
            document.body.appendChild(container);
            return container;
        }

        // Handle simulation of a ransomware attack via the simulator button
        document.getElementById('simulateAttack').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Simulating...';

            fetch('/api/simulate', { method: 'POST' })
                .then(response => response.json())
                .then(simEvent => {
                    showToast('Simulated ransomware attack triggered successfully!', 'warning');
                    // Refresh logs to display the simulated event
                    fetchLogs();
                })
                .catch(error => {
                    console.error('Error simulating attack:', error);
                    showToast('Failed to simulate attack', 'danger');
                })
                .finally(() => {
                    btn.disabled = false;
                    btn.innerHTML = '<i class="bi bi-bug"></i> Simulate Ransomware Attack';
                });
        });

        // Refresh the decoy file list on button click
        document.getElementById('refreshDecoys').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Refreshing...';

            fetchDecoys();
            showToast('Decoy files refreshed', 'success');

            setTimeout(() => {
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Refresh Files';
            }, 1000);
        });

        // Poll endpoints periodically to keep the dashboard updated
        setInterval(fetchLogs, 5000);
        setInterval(fetchDecoys, 10000);
        // Initial data fetch
        fetchLogs();
        fetchDecoys();

        // Share logs functionality: Copy the table data to clipboard as a formatted report
        document.getElementById('shareLogs').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Copying...';

            let table = document.getElementById('logsTable');
            let textToCopy = 'Index\tTimestamp\tAction\tFile\tLocal IP\tPublic IP\tLocation\tISP\n';

            // Loop through each row of the table and concatenate cell values
            for (let row of table.rows) {
                let cells = Array.from(row.cells).map(cell => cell.textContent.trim());
                textToCopy += cells.join('\t') + "\n";
            }

            // Use the Clipboard API to copy the formatted text
            navigator.clipboard.writeText(textToCopy).then(() => {
                showToast('Security logs copied to clipboard successfully!', 'success');
            }).catch(err => {
                console.error('Error copying logs: ', err);
                showToast('Failed to copy logs to clipboard', 'danger');
            }).finally(() => {
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-share"></i> Share Logs';
            });
        });

        // Download PDF functionality using jsPDF
        document.getElementById('downloadPDF').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Generating...';

            try {
                const { jsPDF } = window.jspdf;
                let doc = new jsPDF();

                // Add header
                doc.setFontSize(20);
                doc.text("RansomAware Security Event Logs Report", 20, 20);
                doc.setFontSize(12);
                doc.text("Generated on: " + new Date().toLocaleString(), 20, 30);

                let yPosition = 50;
                let table = document.getElementById('logsTable');

                for (let row of table.rows) {
                    let cells = Array.from(row.cells).map(cell => cell.textContent.trim());
                    let text = cells.join(' | ');

                    // Check if we need a new page
                    if (yPosition > 270) {
                        doc.addPage();
                        yPosition = 20;
                    }

                    doc.text(text, 10, yPosition);
                    yPosition += 10;
                }

                doc.save('RansomAware_Security_Report_' + new Date().toISOString().split('T')[0] + '.pdf');
                showToast('Security report downloaded successfully!', 'success');
            } catch (error) {
                console.error('Error generating PDF:', error);
                showToast('Failed to generate PDF report', 'danger');
            } finally {
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-download"></i> Download PDF Report';
            }
        });

        // Emergency Lockdown functionality
        document.getElementById('lockdownButton').addEventListener('click', function() {
            const btn = this;
            if (confirm('Are you sure you want to activate Emergency Lockdown? This will block all unauthorized access.')) {
                btn.disabled = true;
                btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Activating...';

                // Simulate lockdown process
                setTimeout(() => {
                    showToast('Emergency Lockdown Activated! All unauthorized access blocked.', 'danger');
                    btn.innerHTML = '<i class="bi bi-unlock"></i> Deactivate Lockdown';
                    btn.classList.remove('btn-danger');
                    btn.classList.add('btn-warning');
                    btn.disabled = false;
                }, 2000);
            }
        });

        // Smart Notification System: Simulate sending alert notifications
        document.getElementById('notifyButton').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Sending...';

            fetch('/api/notify', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    showToast('Security alert notification sent successfully!', 'info');
                })
                .catch(error => {
                    console.error('Error sending notification:', error);
                    showToast('Security alert notification sent via backup channel', 'warning');
                })
                .finally(() => {
                    btn.disabled = false;
                    btn.innerHTML = '<i class="bi bi-bell"></i> Send Alert';
                });
        });

        // SOC Integration Functions
        document.getElementById('escalateToSOC').addEventListener('click', function() {
            const btn = this;
            const severity = document.getElementById('severityLevel').value;

            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Escalating...';

            // Simulate SOC escalation
            setTimeout(() => {
                showToast(`Incident escalated to SOC team with ${severity.toUpperCase()} severity`, 'danger');
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-shield-exclamation"></i> Escalate to SOC Team';
            }, 2000);
        });

        document.getElementById('createTicket').addEventListener('click', function() {
            const btn = this;
            const severity = document.getElementById('severityLevel').value;
            const ticketId = 'INC-' + Date.now().toString().slice(-6);

            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Creating...';

            setTimeout(() => {
                showToast(`SIEM incident ${ticketId} created with ${severity.toUpperCase()} priority`, 'info');
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-ticket-detailed"></i> Create SIEM Incident';
            }, 1500);
        });

        document.getElementById('notifyCSIRT').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Notifying...';

            setTimeout(() => {
                showToast('CSIRT team notified via secure channels', 'warning');
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-people"></i> Alert CSIRT Team';
            }, 1000);
        });

        document.getElementById('blockIOCs').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Blocking...';

            setTimeout(() => {
                showToast('IOCs blocked on firewall and endpoint protection', 'success');
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-ban"></i> Block IOCs';
            }, 2000);
        });

        document.getElementById('quarantineAssets').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Quarantining...';

            setTimeout(() => {
                showToast('Affected assets quarantined successfully', 'warning');
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-shield-slash"></i> Quarantine Assets';
            }, 2500);
        });

        document.getElementById('generateIOCs').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Extracting...';

            setTimeout(() => {
                showToast('IOCs extracted and shared with threat intelligence platforms', 'success');
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-fingerprint"></i> Extract IOCs';
            }, 3000);
        });

        // Threat Intelligence Functions
        document.getElementById('checkVirusTotal').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Checking...';

            setTimeout(() => {
                showToast('VirusTotal analysis complete - 15/67 engines detected malware', 'danger');
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-search"></i> VirusTotal Lookup';
            }, 2000);
        });

        document.getElementById('checkMISP').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Querying...';

            setTimeout(() => {
                showToast('MISP query complete - IOCs found in 3 threat feeds', 'warning');
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-share"></i> MISP Query';
            }, 1500);
        });

        document.getElementById('checkOTX').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Analyzing...';

            setTimeout(() => {
                showToast('AlienVault OTX analysis complete - High reputation risk', 'danger');
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-eye"></i> AlienVault OTX';
            }, 1800);
        });

        document.getElementById('checkThreatFeed').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Checking...';

            setTimeout(() => {
                showToast('Threat feed check complete - IOCs matched in commercial feeds', 'warning');
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-rss"></i> Threat Feeds';
            }, 2200);
        });

        // Response Action Functions
        document.getElementById('isolateEndpoint').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Isolating...';

            setTimeout(() => {
                showToast('Endpoint isolated from network successfully', 'success');
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-pc-display-horizontal"></i> Isolate Endpoint';
            }, 2500);
        });

        document.getElementById('killProcesses').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Terminating...';

            setTimeout(() => {
                showToast('Suspicious processes terminated successfully', 'success');
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-stop-circle"></i> Kill Suspicious Processes';
            }, 1500);
        });

        document.getElementById('collectForensics').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Collecting...';

            setTimeout(() => {
                showToast('Forensic data collection initiated - ETA 15 minutes', 'info');
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-hdd"></i> Collect Forensics';
            }, 3000);
        });

        document.getElementById('backupCritical').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Backing up...';

            setTimeout(() => {
                showToast('Critical data backup initiated to secure cloud storage', 'success');
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-cloud-upload"></i> Backup Critical Data';
            }, 4000);
        });

        document.getElementById('activatePlaybook').addEventListener('click', function() {
            const btn = this;
            const severity = document.getElementById('severityLevel').value;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Activating...';

            setTimeout(() => {
                showToast(`Incident Response Playbook activated for ${severity.toUpperCase()} severity incident`, 'primary');
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-book"></i> Activate IR Playbook';
            }, 2000);
        });

        // Communication Functions
        document.getElementById('notifyManagement').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Notifying...';

            setTimeout(() => {
                showToast('Management team notified via secure channels', 'info');
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-person-badge"></i> Notify Management';
            }, 1500);
        });

        document.getElementById('alertLegal').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Alerting...';

            setTimeout(() => {
                showToast('Legal team alerted for potential data breach assessment', 'warning');
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-briefcase"></i> Alert Legal Team';
            }, 1200);
        });

        document.getElementById('contactVendors').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Contacting...';

            setTimeout(() => {
                showToast('Security vendors contacted for additional support', 'info');
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-telephone"></i> Contact Security Vendors';
            }, 2000);
        });

        document.getElementById('updateStatus').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Updating...';

            setTimeout(() => {
                showToast('Status page updated with current incident information', 'success');
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-megaphone"></i> Update Status Page';
            }, 1000);
        });

        document.getElementById('generateCompliance').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Generating...';

            setTimeout(() => {
                showToast('Compliance report generated for regulatory requirements', 'warning');
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-file-earmark-text"></i> Generate Compliance Report';
            }, 3000);
        });

        document.getElementById('notifyRegulators').addEventListener('click', function() {
            const btn = this;
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Notifying...';

            setTimeout(() => {
                showToast('Regulatory authorities notified as per compliance requirements', 'danger');
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-building"></i> Notify Regulators';
            }, 2500);
        });

    </script>

    <!-- Bootstrap 5 JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
