<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>RansomAware Security Dashboard</title>
    <!-- Bootstrap 5 for modern UI -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Chart.js for graphs -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- jsPDF for PDF generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-bg: #0f172a;
            --card-bg: #1e293b;
            --border-color: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        body {
            background: linear-gradient(135deg, var(--dark-bg) 0%, #1e293b 100%);
            color: var(--text-primary);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        .navbar {
            background: rgba(15, 23, 42, 0.95) !important;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-color);
            box-shadow: var(--shadow);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--text-primary) !important;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-online {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .status-indicator {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: currentColor;
        }

        .main-container {
            padding: 2rem 0;
        }

        .card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.25);
        }

        .card-header {
            background: rgba(37, 99, 235, 0.1);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 1.5rem;
            border-radius: 0;
        }

        .card-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-primary);
        }

        .card-body {
            padding: 1.5rem;
        }

        .table-dark {
            --bs-table-bg: transparent;
            --bs-table-border-color: var(--border-color);
            margin: 0;
        }

        .table-dark th {
            background: rgba(37, 99, 235, 0.1);
            border-color: var(--border-color);
            font-weight: 600;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 0.75rem;
            color: var(--text-secondary);
        }

        .table-dark td {
            border-color: var(--border-color);
            vertical-align: middle;
            padding: 0.75rem;
            font-size: 0.875rem;
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
            padding: 0.5rem 1rem;
            transition: all 0.2s ease;
            border: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
            color: white;
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: #000;
        }

        .btn-warning:hover {
            background: #d97706;
            color: #000;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-secondary {
            background: var(--secondary-color);
            color: white;
        }

        .btn-outline-primary {
            background: transparent;
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            color: white;
        }

        /* Alert animations */
        .blinking {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                background-color: rgba(239, 68, 68, 0.1);
                color: var(--danger-color);
            }
            50% {
                background-color: rgba(239, 68, 68, 0.3);
                color: #fff;
            }
            100% {
                background-color: rgba(239, 68, 68, 0.1);
                color: var(--danger-color);
            }
        }

        .scrollable-container {
            max-height: 400px;
            overflow-y: auto;
            border-radius: 8px;
        }

        .scrollable-container::-webkit-scrollbar {
            width: 6px;
        }

        .scrollable-container::-webkit-scrollbar-track {
            background: var(--card-bg);
        }

        .scrollable-container::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        .alert {
            border: none;
            border-radius: 8px;
            font-weight: 500;
            margin: 0;
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }

        .chart-container {
            position: relative;
            height: 300px;
            padding: 1rem 0;
        }

        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .metric-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
        }

        .metric-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .action-buttons {
            display: flex;
            gap: 0.75rem;
            flex-wrap: wrap;
            margin-top: 1rem;
        }

        .section-divider {
            margin: 2rem 0;
        }

        .badge {
            font-weight: 500;
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
        }

        .hidden {
            display: none !important;
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }

            .card-body {
                padding: 1rem;
            }

            .action-buttons {
                flex-direction: column;
            }

            .btn {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-shield-check"></i>
                RansomAware Security Dashboard
            </a>
            <div class="ms-auto">
                <span class="status-badge status-online">
                    <span class="status-indicator"></span>
                    System Online
                </span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container main-container">
        <!-- Metrics Overview -->
        <div class="metric-grid">
            <div class="metric-card">
                <div class="metric-value" id="totalEvents">0</div>
                <div class="metric-label">Total Events</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="activeAlerts">0</div>
                <div class="metric-label">Active Alerts</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="decoyFiles">0</div>
                <div class="metric-label">Decoy Files</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="systemUptime">99.9%</div>
                <div class="metric-label">System Uptime</div>
            </div>
        </div>

        <!-- Event Logs & Intrusion Alerts -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="bi bi-list-ul"></i>
                            Security Event Logs
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="scrollable-container">
                            <table class="table table-dark table-hover" id="logsTable">
                                <thead>
                                    <tr>
                                        <th>Index</th>
                                        <th>Timestamp</th>
                                        <th>Action</th>
                                        <th>File</th>
                                        <th>Local IP</th>
                                        <th>Public IP</th>
                                        <th>Location</th>
                                        <th>ISP</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Log entries will be injected here dynamically -->
                                </tbody>
                            </table>
                        </div>
                        <div class="action-buttons">
                            <button id="shareLogs" class="btn btn-outline-primary">
                                <i class="bi bi-share"></i>
                                Share Logs
                            </button>
                            <button id="downloadPDF" class="btn btn-secondary">
                                <i class="bi bi-download"></i>
                                Download PDF Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="bi bi-exclamation-triangle"></i>
                            Intrusion Alerts
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="scrollable-container" style="max-height: 300px;">
                            <div id="alerts" class="alert alert-danger hidden"></div>
                            <div id="noAlerts" class="text-center text-muted py-4">
                                <i class="bi bi-shield-check" style="font-size: 2rem;"></i>
                                <p class="mt-2 mb-0">No active alerts</p>
                            </div>
                        </div>
                        <div class="action-buttons">
                            <button id="lockdownButton" class="btn btn-danger">
                                <i class="bi bi-lock"></i>
                                Emergency Lockdown
                            </button>
                            <button id="notifyButton" class="btn btn-warning">
                                <i class="bi bi-bell"></i>
                                Send Alert
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Digital Twin Integrity Status -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="bi bi-cpu"></i>
                            Digital Twin Integrity Status
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="twinStatus" class="alert alert-success">
                            <i class="bi bi-check-circle"></i>
                            All decoy files are consistent with the baseline.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Event Frequency Chart -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="bi bi-graph-up"></i>
                            Event Frequency Analysis
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="eventsChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- File Interaction Simulator & Decoy Control Panel -->
        <div class="row">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="bi bi-play-circle"></i>
                            Security Testing
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-3">Simulate ransomware attacks to test system response and detection capabilities.</p>
                        <button id="simulateAttack" class="btn btn-warning">
                            <i class="bi bi-bug"></i>
                            Simulate Ransomware Attack
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="bi bi-files"></i>
                            Decoy File Management
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="scrollable-container" style="max-height: 200px;">
                            <table class="table table-dark table-sm" id="decoysTable">
                                <thead>
                                    <tr>
                                        <th>Decoy File Name</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Decoy file list will be injected here -->
                                </tbody>
                            </table>
                        </div>
                        <div class="action-buttons">
                            <button id="refreshDecoys" class="btn btn-primary">
                                <i class="bi bi-arrow-clockwise"></i>
                                Refresh Files
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- jQuery (required for Bootstrap's JavaScript plugins) -->
    <script src="https://code.jquery.com/jquery-3.2.1.slim.min.js"></script>
    
    <script>
        let chart;  // Global variable for Chart.js instance

        // Update metrics in the dashboard
        function updateMetrics(data, alerts, decoyCount) {
            document.getElementById('totalEvents').textContent = data.length;
            document.getElementById('activeAlerts').textContent = alerts.length;
            document.getElementById('decoyFiles').textContent = decoyCount;
        }

        // Fetch logs from the backend API and update Digital Twin status
        function fetchLogs() {
            fetch('/api/logs')
                .then(response => response.json())
                .then(data => {
                    const tableBody = document.querySelector('#logsTable tbody');
                    if (!tableBody) return; // Prevent errors if tbody is missing
                    tableBody.innerHTML = '';
                    let eventCounts = {};
                    let alerts = [];
                    let twinFlag = false; // Flag for digital twin deviations
                    let twinAlertFiles = [];  // To store details of files causing TWIN_ALERT

                    data.forEach((block) => {
                        const row = document.createElement('tr');
                        const date = new Date(block.timestamp * 1000);
                        const timeStr = date.toLocaleString();
                        const action = block.event_data ? block.event_data.action : 'N/A';
                        const file = block.event_data ? block.event_data.file : 'N/A';

                        const ipInfo = (block.event_data && block.event_data.ip_info) ? block.event_data.ip_info : {};
                        const { local_ip = "N/A", public_ip = "N/A", location = "N/A", isp = "N/A" } = ipInfo;

                        // Add action badge styling
                        let actionBadge = '';
                        if (action === 'ANOMALOUS_ACTIVITY') {
                            actionBadge = `<span class="badge bg-danger">${action}</span>`;
                        } else if (action === 'TWIN_ALERT') {
                            actionBadge = `<span class="badge bg-warning">${action}</span>`;
                        } else if (action === 'SIMULATED_ATTACK') {
                            actionBadge = `<span class="badge bg-info">${action}</span>`;
                        } else {
                            actionBadge = `<span class="badge bg-secondary">${action}</span>`;
                        }

                        row.innerHTML = `
                            <td><span class="badge bg-primary">${block.index}</span></td>
                            <td><small>${timeStr}</small></td>
                            <td>${actionBadge}</td>
                            <td><small>${file}</small></td>
                            <td><small>${local_ip}</small></td>
                            <td><small>${public_ip}</small></td>
                            <td><small>${location}</small></td>
                            <td><small>${isp}</small></td>
                        `;

                        // If it's a digital twin alert, record its file details
                        if (action === "TWIN_ALERT") {
                            twinFlag = true;
                            if (file && file.trim() !== "" && file !== "N/A") {
                                twinAlertFiles.push(file);
                            }
                        }

                        // Highlight rows with any alert and add to alerts array
                        if (action === "ANOMALOUS_ACTIVITY" || action === "SIMULATED_ATTACK" || action === "TWIN_ALERT") {
                            row.classList.add('blinking');
                            alerts.push(`<i class="bi bi-exclamation-triangle"></i> ${action} detected on ${file}`);
                        }

                        tableBody.appendChild(row);

                        // Group events for chart by hour:minute
                        if (block.event_data) {
                            const timeKey = date.getHours() + ':' + String(date.getMinutes()).padStart(2, '0');
                            eventCounts[timeKey] = (eventCounts[timeKey] || 0) + 1;
                        }
                    });

                    updateChart(eventCounts);
                    updateAlerts(alerts);

                    // Update Digital Twin Integrity Status
                    const twinDiv = document.getElementById('twinStatus');
                    if (twinFlag && twinAlertFiles.length > 0) {
                        let listHtml = "<i class='bi bi-exclamation-triangle'></i> <strong>Alert: Deviations detected in digital twin for file(s):</strong><br>";
                        listHtml += "<div style='max-height:150px; overflow-y:auto; margin-top: 0.5rem;'><ul class='mb-0'>";
                        twinAlertFiles.forEach(item => {
                            listHtml += "<li>" + item + "</li>";
                        });
                        listHtml += "</ul></div>";
                        twinDiv.innerHTML = listHtml;
                        twinDiv.className = "alert alert-danger";
                    } else if (twinFlag) {
                        twinDiv.innerHTML = "<i class='bi bi-exclamation-triangle'></i> Alert: Deviations detected in digital twin!";
                        twinDiv.className = "alert alert-danger";
                    } else {
                        twinDiv.innerHTML = "<i class='bi bi-check-circle'></i> All decoy files are consistent with the baseline.";
                        twinDiv.className = "alert alert-success";
                    }

                    // Update metrics
                    fetch('/api/decoys')
                        .then(response => response.json())
                        .then(decoys => {
                            updateMetrics(data, alerts, decoys.length);
                        })
                        .catch(() => updateMetrics(data, alerts, 0));
                })
                .catch(error => console.error('Error fetching logs:', error));
        }
        
        // Update or create the Chart.js bar chart for event frequency
        function updateChart(data) {
            const labels = Object.keys(data);
            const counts = Object.values(data);
            const ctx = document.getElementById('eventsChart').getContext('2d');

            if (chart) {
                chart.data.labels = labels;
                chart.data.datasets[0].data = counts;
                chart.update();
            } else {
                chart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Events per Minute',
                            data: counts,
                            backgroundColor: 'rgba(37, 99, 235, 0.1)',
                            borderColor: 'rgba(37, 99, 235, 1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4,
                            pointBackgroundColor: 'rgba(37, 99, 235, 1)',
                            pointBorderColor: '#fff',
                            pointBorderWidth: 2,
                            pointRadius: 4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                labels: {
                                    color: '#cbd5e1'
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    precision: 0,
                                    color: '#cbd5e1'
                                },
                                grid: {
                                    color: 'rgba(51, 65, 85, 0.5)'
                                }
                            },
                            x: {
                                ticks: {
                                    color: '#cbd5e1'
                                },
                                grid: {
                                    color: 'rgba(51, 65, 85, 0.5)'
                                }
                            }
                        }
                    }
                });
            }
        }

        // Update the Intrusion Alerts panel
        function updateAlerts(alerts) {
            const alertsDiv = document.getElementById('alerts');
            const noAlertsDiv = document.getElementById('noAlerts');

            if (alerts.length > 0) {
                alertsDiv.style.display = 'block';
                noAlertsDiv.style.display = 'none';
                alertsDiv.innerHTML = alerts.join('<hr class="my-2">');
            } else {
                alertsDiv.style.display = 'none';
                noAlertsDiv.style.display = 'block';
            }
        }
        
        // Fetch decoy file names from the backend
        function fetchDecoys() {
            fetch('/api/decoys')
                .then(response => response.json())
                .then(files => {
                    const decoysTable = document.querySelector('#decoysTable tbody');
                    decoysTable.innerHTML = '';
                    files.forEach(file => {
                        const row = document.createElement('tr');
                        row.innerHTML = `<td>${file}</td>`;
                        decoysTable.appendChild(row);
                    });
                })
                .catch(error => console.error('Error fetching decoy files:', error));
        }
        
        // Handle simulation of a ransomware attack via the simulator button
        document.getElementById('simulateAttack').addEventListener('click', function() {
            fetch('/api/simulate', { method: 'POST' })
                .then(response => response.json())
                .then(simEvent => {
                    alert('Simulated ransomware attack triggered!');
                    // Refresh logs to display the simulated event
                    fetchLogs();
                })
                .catch(error => console.error('Error simulating attack:', error));
        });
        
        // Refresh the decoy file list on button click
        document.getElementById('refreshDecoys').addEventListener('click', function() {
            fetchDecoys();
        });
        
        // Poll endpoints periodically to keep the dashboard updated
        setInterval(fetchLogs, 5000);
        setInterval(fetchDecoys, 10000);
        // Initial data fetch
        fetchLogs();
        fetchDecoys();

        // Share logs functionality: Copy the table data to clipboard as a formatted report
        document.getElementById('shareLogs').addEventListener('click', function() {
            let table = document.getElementById('logsTable');
            let textToCopy = 'Index\tTimestamp\tAction\tFile\tLocal IP\tPublic IP\tLocation\tISP\n';
    
            // Loop through each row of the table and concatenate cell values
            for (let row of table.rows) {
                let cells = Array.from(row.cells).map(cell => cell.textContent.trim());
                textToCopy += cells.join('\t') + "\n";
            }
    
            // Use the Clipboard API to copy the formatted text
            navigator.clipboard.writeText(textToCopy).then(() => {
                alert('Logs copied to clipboard!');
            }).catch(err => {
                console.error('Error copying logs: ', err);
            });
        });

        // Download PDF functionality using jsPDF
        document.getElementById('downloadPDF').addEventListener('click', function() {
            const { jsPDF } = window.jspdf;
            let doc = new jsPDF();
            let text = "Event Logs Report\n\n";
            let table = document.getElementById('logsTable');
            for (let row of table.rows) {
                let cells = Array.from(row.cells).map(cell => cell.textContent.trim());
                text += cells.join(' | ') + "\n";
            }
            doc.text(text, 10, 10);
            doc.save('Event_Logs_Report.pdf');
        });

        
        // Emergency Lockdown functionality
        document.getElementById('lockdownButton').addEventListener('click', function() {
            alert('Emergency Lockdown Activated! All unauthorized access will be blocked.');
        });

        // Smart Notification System: Simulate sending alert notifications
        document.getElementById('notifyButton').addEventListener('click', function() {
            fetch('/api/notify', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    alert('Notification sent: ' + data.message);
                })
                .catch(error => console.error('Error sending notification:', error));
        });
    </script>
</body>
</html>
