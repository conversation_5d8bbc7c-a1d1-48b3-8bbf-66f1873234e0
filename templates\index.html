<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>RansomAware Security Dashboard</title>
    <!-- Bootstrap 5 for modern UI -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Chart.js for graphs -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- jsPDF for PDF generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-bg: #0f172a;
            --card-bg: #1e293b;
            --border-color: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        body {
            background: linear-gradient(135deg, var(--dark-bg) 0%, #1e293b 100%);
            color: var(--text-primary);
            min-height: 100vh;
        }

        .navbar {
            background: rgba(15, 23, 42, 0.95) !important;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-color);
            box-shadow: var(--shadow);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--text-primary) !important;
        }

        .card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.25);
        }

        .card-header {
            background: rgba(37, 99, 235, 0.1);
            border-bottom: 1px solid var(--border-color);
            border-radius: 12px 12px 0 0 !important;
            padding: 1rem 1.5rem;
        }

        .card-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
        }

        .status-online { background-color: var(--success-color); }
        .status-warning { background-color: var(--warning-color); }
        .status-danger { background-color: var(--danger-color); }

        .table-dark {
            --bs-table-bg: transparent;
            --bs-table-border-color: var(--border-color);
        }

        .table-dark th {
            background: rgba(37, 99, 235, 0.1);
            border-color: var(--border-color);
            font-weight: 600;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .table-dark td {
            border-color: var(--border-color);
            vertical-align: middle;
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
            padding: 0.5rem 1rem;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background: #1d4ed8;
            border-color: #1d4ed8;
            transform: translateY(-1px);
        }

        .btn-danger {
            background: var(--danger-color);
            border-color: var(--danger-color);
        }

        .btn-warning {
            background: var(--warning-color);
            border-color: var(--warning-color);
            color: #000;
        }

        .btn-success {
            background: var(--success-color);
            border-color: var(--success-color);
        }

        .btn-secondary {
            background: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        /* Alert animations */
        .blinking {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                background-color: rgba(239, 68, 68, 0.1);
                color: var(--danger-color);
            }
            50% {
                background-color: rgba(239, 68, 68, 0.3);
                color: #fff;
            }
            100% {
                background-color: rgba(239, 68, 68, 0.1);
                color: var(--danger-color);
            }
        }

        .scrollable-container {
            max-height: 400px;
            overflow-y: auto;
            border-radius: 8px;
        }

        .scrollable-container::-webkit-scrollbar {
            width: 6px;
        }

        .scrollable-container::-webkit-scrollbar-track {
            background: var(--card-bg);
        }

        .scrollable-container::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        .alert {
            border: none;
            border-radius: 8px;
            font-weight: 500;
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }

        .metric-card {
            text-align: center;
            padding: 1.5rem;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .metric-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .section-divider {
            margin: 2rem 0;
        }

        .badge {
            font-weight: 500;
            padding: 0.375rem 0.75rem;
        }

        .chart-container {
            position: relative;
            height: 300px;
            padding: 1rem;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">RansomAware Dashboard</h1>
        
        <!-- Row 1: Event Logs & Intrusion Alerts -->
        <div class="row section">
            <div class="col-md-6">
                <h3>Event Logs</h3>
                <div class="scrollable-table">
                    <table class="table table-dark table-striped" id="logsTable">
                        <thead>
                            <tr>
                                <th>Index</th>
                                <th>Timestamp</th>
                                <th>Action</th>
                                <th>File</th>
                                <th>Local IP</th>
                                <th>Public IP</th>
                                <th>Location</th>
                                <th>ISP</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Log entries will be injected here dynamically -->
                        </tbody>
                    </table>
                </div>
                <button id="shareLogs" class="btn btn-info mb-2">Share Logs</button>
                <button id="downloadPDF" class="btn btn-secondary mb-2">Download PDF Report</button>
            </div>
            <div class="col-md-6">
                <h3>Intrusion Alerts</h3>
                <div class="scrollable-alerts">
                    <div id="alerts" class="alert alert-danger hidden"></div>
                </div>
                <button id="lockdownButton" class="btn btn-danger mt-2">Emergency Lockdown</button>
                
                <button id="notifyButton" class="btn btn-warning mt-2">Send Alert Notification</button>
            </div>
        </div>
        
        <!-- Row 2: Digital Twin Integrity Status -->
        <div class="row section">
            <div class="col-md-12">
                <h3>Digital Twin Integrity Status</h3>
                <div id="twinStatus" class="alert alert-success">
                    All decoy files are consistent with the baseline.
                </div>
            </div>
        </div>
        
        <!-- Row 3: Event Frequency Chart -->
        <div class="row section">
            <div class="col-md-12">
                <h3>Event Frequency Chart</h3>
                <canvas id="eventsChart" width="400" height="200"></canvas>
            </div>
        </div>
        
        <!-- Row 4: File Interaction Simulator -->
        <div class="row section">
            <div class="col-md-12">
                <h3>File Interaction Simulator</h3>
                <button id="simulateAttack" class="btn btn-warning">Simulate Ransomware Attack</button>
            </div>
        </div>
        
        <!-- Row 5: Decoy File Control Panel -->
        <div class="row section">
            <div class="col-md-12">
                <h3>Decoy File Control Panel</h3>
                <div class="scrollable-decoys">
                    <table class="table table-dark table-striped" id="decoysTable">
                        <thead>
                            <tr>
                                <th>Decoy File Name</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Decoy file list will be injected here -->
                        </tbody>
                    </table>
                </div>
                <button id="refreshDecoys" class="btn btn-primary mt-2">Refresh Decoy Files</button>
            </div>
        </div>
    </div>
    
    <!-- jQuery (required for Bootstrap's JavaScript plugins) -->
    <script src="https://code.jquery.com/jquery-3.2.1.slim.min.js"></script>
    
    <script>
        let chart;  // Global variable for Chart.js instance

        // Fetch logs from the backend API and update Digital Twin status
        function fetchLogs() {
            fetch('/api/logs')
                .then(response => response.json())
                .then(data => {
                    const tableBody = document.querySelector('#logsTable tbody');
                    if (!tableBody) return; // Prevent errors if tbody is missing
                    tableBody.innerHTML = '';
                    let eventCounts = {};
                    let alerts = [];
                    let twinFlag = false; // Flag for digital twin deviations
                    let twinAlertFiles = [];  // To store details of files causing TWIN_ALERT

                    data.forEach((block) => {
                        const row = document.createElement('tr');
                        const date = new Date(block.timestamp * 1000);
                        const timeStr = date.toLocaleString();
                        const action = block.event_data ? block.event_data.action : 'N/A';
                        const file = block.event_data ? block.event_data.file : 'N/A';

                        const ipInfo = (block.event_data && block.event_data.ip_info) ? block.event_data.ip_info : {};
                        const { local_ip = "N/A", public_ip = "N/A", location = "N/A", isp = "N/A" } = ipInfo;

                        row.innerHTML = `
                            <td>${block.index}</td>
                            <td>${timeStr}</td>
                            <td>${action}</td>
                            <td>${file}</td>
                            <td>${local_ip}</td>
                            <td>${public_ip}</td>
                            <td>${location}</td>
                            <td>${isp}</td>
                        `;
                        
                        // If it's a digital twin alert, record its file details
                        if (action === "TWIN_ALERT") {
                            twinFlag = true;
                            if (file && file.trim() !== "" && file !== "N/A") {
                                twinAlertFiles.push(file);
                            }
                        }
                        
                        // Highlight rows with any alert and add to alerts array
                        if (action === "ANOMALOUS_ACTIVITY" || action === "SIMULATED_ATTACK" || action === "TWIN_ALERT") {
                            row.classList.add('blinking');
                            alerts.push(`Alert: ${action} on ${file}`);
                        }
                        
                        tableBody.appendChild(row);
                        
                        // Group events for chart by hour:minute
                        if (block.event_data) {
                            const timeKey = date.getHours() + ':' + date.getMinutes();
                            eventCounts[timeKey] = (eventCounts[timeKey] || 0) + 1;
                        }
                    });
                    updateChart(eventCounts);
                    updateAlerts(alerts);
                    
                    // Update Digital Twin Integrity Status as a scrollable list
                    const twinDiv = document.getElementById('twinStatus');
                    if (twinFlag && twinAlertFiles.length > 0) {
                        let listHtml = "<strong>Alert: Deviations detected in digital twin for file(s):</strong><br>";
                        listHtml += "<div style='max-height:150px; overflow-y:auto;'><ul>";
                        twinAlertFiles.forEach(item => {
                            listHtml += "<li>" + item + "</li>";
                        });
                        listHtml += "</ul></div>";
                        twinDiv.innerHTML = listHtml;
                        twinDiv.className = "alert alert-danger";
                    } else if (twinFlag) {
                        twinDiv.innerHTML = "Alert: Deviations detected in digital twin!";
                        twinDiv.className = "alert alert-danger";
                    } else {
                        twinDiv.innerHTML = "All decoy files are consistent with the baseline.";
                        twinDiv.className = "alert alert-success";
                    }
                })
                .catch(error => console.error('Error fetching logs:', error));
        }
        
        // Update or create the Chart.js bar chart for event frequency
        function updateChart(data) {
            const labels = Object.keys(data);
            const counts = Object.values(data);
            const ctx = document.getElementById('eventsChart').getContext('2d');
            
            if (chart) {
                chart.data.labels = labels;
                chart.data.datasets[0].data = counts;
                chart.update();
            } else {
                chart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Events per Minute',
                            data: counts,
                            backgroundColor: 'rgba(255, 99, 132, 0.6)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        scales: {
                            yAxes: [{
                                ticks: {
                                    beginAtZero: true,
                                    precision: 0
                                }
                            }]
                        }
                    }
                });
            }
        }
        
        // Update the Intrusion Alerts panel
        function updateAlerts(alerts) {
            const alertsDiv = document.getElementById('alerts');
            if (alerts.length > 0) {
                alertsDiv.style.display = 'block';
                alertsDiv.innerHTML = alerts.join('<br>');
            } else {
                alertsDiv.style.display = 'none';
            }
        }
        
        // Fetch decoy file names from the backend
        function fetchDecoys() {
            fetch('/api/decoys')
                .then(response => response.json())
                .then(files => {
                    const decoysTable = document.querySelector('#decoysTable tbody');
                    decoysTable.innerHTML = '';
                    files.forEach(file => {
                        const row = document.createElement('tr');
                        row.innerHTML = `<td>${file}</td>`;
                        decoysTable.appendChild(row);
                    });
                })
                .catch(error => console.error('Error fetching decoy files:', error));
        }
        
        // Handle simulation of a ransomware attack via the simulator button
        document.getElementById('simulateAttack').addEventListener('click', function() {
            fetch('/api/simulate', { method: 'POST' })
                .then(response => response.json())
                .then(simEvent => {
                    alert('Simulated ransomware attack triggered!');
                    // Refresh logs to display the simulated event
                    fetchLogs();
                })
                .catch(error => console.error('Error simulating attack:', error));
        });
        
        // Refresh the decoy file list on button click
        document.getElementById('refreshDecoys').addEventListener('click', function() {
            fetchDecoys();
        });
        
        // Poll endpoints periodically to keep the dashboard updated
        setInterval(fetchLogs, 5000);
        setInterval(fetchDecoys, 10000);
        // Initial data fetch
        fetchLogs();
        fetchDecoys();

        // Share logs functionality: Copy the table data to clipboard as a formatted report
        document.getElementById('shareLogs').addEventListener('click', function() {
            let table = document.getElementById('logsTable');
            let textToCopy = 'Index\tTimestamp\tAction\tFile\tLocal IP\tPublic IP\tLocation\tISP\n';
    
            // Loop through each row of the table and concatenate cell values
            for (let row of table.rows) {
                let cells = Array.from(row.cells).map(cell => cell.textContent.trim());
                textToCopy += cells.join('\t') + "\n";
            }
    
            // Use the Clipboard API to copy the formatted text
            navigator.clipboard.writeText(textToCopy).then(() => {
                alert('Logs copied to clipboard!');
            }).catch(err => {
                console.error('Error copying logs: ', err);
            });
        });

        // Download PDF functionality using jsPDF
        document.getElementById('downloadPDF').addEventListener('click', function() {
            const { jsPDF } = window.jspdf;
            let doc = new jsPDF();
            let text = "Event Logs Report\n\n";
            let table = document.getElementById('logsTable');
            for (let row of table.rows) {
                let cells = Array.from(row.cells).map(cell => cell.textContent.trim());
                text += cells.join(' | ') + "\n";
            }
            doc.text(text, 10, 10);
            doc.save('Event_Logs_Report.pdf');
        });

        
        // Emergency Lockdown functionality
        document.getElementById('lockdownButton').addEventListener('click', function() {
            alert('Emergency Lockdown Activated! All unauthorized access will be blocked.');
        });

        // Smart Notification System: Simulate sending alert notifications
        document.getElementById('notifyButton').addEventListener('click', function() {
            fetch('/api/notify', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    alert('Notification sent: ' + data.message);
                })
                .catch(error => console.error('Error sending notification:', error));
        });
    </script>
</body>
</html>
